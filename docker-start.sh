#!/bin/bash

# Docker 快速启动脚本

echo "🚀 启动 Aug-Pay Docker 容器..."

# 检查是否存在 .env.local 文件
if [ ! -f ".env.local" ]; then
    echo "❌ 错误：未找到 .env.local 文件"
    echo "请确保项目根目录下有 .env.local 文件"
    exit 1
fi

# 检查是否存在 certs 目录
if [ ! -d "certs" ]; then
    echo "⚠️  警告：未找到 certs 目录"
    echo "如果需要微信支付功能，请确保 certs 目录下有证书文件"
fi

# 创建数据目录
mkdir -p data

# 设置代理（如果需要）
echo "🌐 配置网络代理..."
export http_proxy="http://127.0.0.1:7890"
export https_proxy="http://127.0.0.1:7890"

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker build -t aug-pay .

if [ $? -ne 0 ]; then
    echo "❌ 镜像构建失败"
    exit 1
fi

# 取消代理（避免影响本地连接）
unset http_proxy
unset https_proxy

# 停止并删除已存在的容器
echo "🧹 清理已存在的容器..."
docker stop aug-pay 2>/dev/null || true
docker rm aug-pay 2>/dev/null || true

# 启动容器
echo "🚀 启动容器..."
docker run -d \
  --name aug-pay \
  -p 3000:3000 \
  -v $(pwd)/.env.local:/app/.env.local:ro \
  -v $(pwd)/certs:/app/certs:ro \
  -v $(pwd)/data:/app/data \
  -e DATABASE_URL=file:./data/dev.db \
  --restart unless-stopped \
  aug-pay

if [ $? -eq 0 ]; then
    echo "✅ 容器启动成功！"
    echo "📱 应用地址: http://localhost:3000"
    echo "🔍 健康检查: http://localhost:3000/api/health"
    echo ""
    echo "📋 常用命令："
    echo "  查看日志: docker logs -f aug-pay"
    echo "  停止容器: docker stop aug-pay"
    echo "  重启容器: docker restart aug-pay"
    echo "  删除容器: docker rm -f aug-pay"
else
    echo "❌ 容器启动失败"
    exit 1
fi
