'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { CheckCircle2, Mail, Copy, X, AlertTriangle, MessageCircle, Phone, Sparkles, Star, Gift } from 'lucide-react';

interface PaymentSuccessModalProps {
  augmentEmail: string;
  userEmail: string;
  onClose: () => void;
}

export default function PaymentSuccessModal({
  augmentEmail,
  userEmail,
  onClose
}: PaymentSuccessModalProps) {
  const [copied, setCopied] = useState(false);
  const [showConfetti, setShowConfetti] = useState(true);
  const [mounted, setMounted] = useState(false);

  const handleCopyEmail = async () => {
    try {
      await navigator.clipboard.writeText(augmentEmail);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  useEffect(() => {
    setMounted(true);
    // 3秒后隐藏彩带动画
    const timer = setTimeout(() => setShowConfetti(false), 3000);
    return () => clearTimeout(timer);
  }, []);

  // 确保只在客户端渲染
  if (!mounted) return null;

  const modalContent = (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      {/* 背景层 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900">
        {/* 浮动几何装饰 */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-blue-400/20 rounded-full animate-float"></div>
        <div className="absolute top-40 right-32 w-24 h-24 bg-purple-400/20 rounded-full animate-float animation-delay-2000"></div>
        <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-emerald-400/20 rounded-full animate-float animation-delay-4000"></div>

        {/* 彩带动画 */}
        {showConfetti && (
          <>
            <div className="absolute top-0 left-1/4 w-2 h-8 bg-yellow-400 animate-confetti"></div>
            <div className="absolute top-0 left-1/2 w-2 h-6 bg-pink-400 animate-confetti" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute top-0 left-3/4 w-2 h-10 bg-blue-400 animate-confetti" style={{animationDelay: '1s'}}></div>
            <div className="absolute top-0 left-1/3 w-2 h-7 bg-green-400 animate-confetti" style={{animationDelay: '1.5s'}}></div>
            <div className="absolute top-0 left-2/3 w-2 h-9 bg-purple-400 animate-confetti" style={{animationDelay: '2s'}}></div>
          </>
        )}
      </div>

      <div className="relative flex min-h-full items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="relative w-full max-w-4xl mx-auto">
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden animate-bounce-in">
            <div className="relative p-8 sm:p-12">
              {/* 关闭按钮 */}
              <button
                onClick={onClose}
                className="absolute top-6 right-6 p-3 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:scale-110 z-10"
                aria-label="关闭"
              >
                <X className="w-6 h-6 text-gray-600" />
              </button>

              {/* 成功图标和标题 */}
              <div className="text-center mb-12">
                <div className="relative mb-8">
                  {/* 主成功图标 */}
                  <div className="w-32 h-32 bg-gradient-to-r from-emerald-500 via-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto shadow-2xl animate-celebrate">
                    <CheckCircle2 className="w-16 h-16 text-white" />
                  </div>

                  {/* 装饰星星 */}
                  <div className="absolute -top-2 -left-2">
                    <Sparkles className="w-8 h-8 text-yellow-400 animate-heartbeat" />
                  </div>
                  <div className="absolute -top-4 -right-4">
                    <Star className="w-6 h-6 text-yellow-500 animate-pulse" />
                  </div>
                  <div className="absolute -bottom-2 -right-2">
                    <Gift className="w-7 h-7 text-pink-400 animate-bounce" />
                  </div>
                </div>

                <h3 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-emerald-600 via-green-600 to-blue-600 bg-clip-text text-transparent mb-4">
                  🎉 支付成功！
                </h3>
                <p className="text-xl sm:text-2xl text-gray-600 font-medium mb-2">
                  您的 AugmentCode 账号已准备就绪
                </p>
                <p className="text-lg text-gray-500">
                  感谢您的信任，让我们开始编程之旅吧！
                </p>
              </div>

              {/* 账号信息展示 */}
              <div className="bg-gradient-to-br from-emerald-50 via-green-50 to-blue-50 rounded-3xl p-8 border border-emerald-200 mb-8 shadow-xl relative overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-200/30 rounded-full -translate-y-16 translate-x-16"></div>

                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <h4 className="text-2xl font-bold text-gray-800 flex items-center space-x-3">
                      <div className="p-2 bg-emerald-100 rounded-xl">
                        <Mail className="w-6 h-6 text-emerald-600" />
                      </div>
                      <span>您的专属账号</span>
                    </h4>
                    <button
                      onClick={handleCopyEmail}
                      className={`flex items-center space-x-2 px-6 py-3 rounded-2xl font-medium transition-all duration-300 transform hover:scale-105 ${
                        copied
                          ? 'bg-green-100 text-green-700 border border-green-300'
                          : 'bg-emerald-100 hover:bg-emerald-200 text-emerald-700 border border-emerald-300'
                      }`}
                    >
                      <Copy className="w-5 h-5" />
                      <span>
                        {copied ? '✓ 已复制' : '复制账号'}
                      </span>
                    </button>
                  </div>

                  <div className="bg-white rounded-2xl p-6 border border-emerald-200 shadow-lg relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-emerald-50/50 to-green-50/50 rounded-2xl"></div>
                    <p className="relative text-gray-800 font-mono text-xl break-all text-center py-2">
                      {augmentEmail}
                    </p>
                  </div>

                  <div className="mt-4 text-center">
                    <p className="text-gray-600 text-sm">
                      💡 这是您的专属 AugmentCode 账号，请妥善保管
                    </p>
                  </div>
                </div>
              </div>

              {/* 使用说明 */}
              <div className="mb-8">
                <h4 className="text-2xl font-bold text-gray-800 text-center mb-8">
                  🚀 开始您的编程之旅
                </h4>

                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-cyan-50 rounded-2xl p-6 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <span className="text-white font-bold text-2xl">1</span>
                      </div>
                      <h5 className="text-lg font-bold text-gray-800 mb-3">账号信息已发送</h5>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        使用此账号登录augmentcode验证码会发送至您的邮箱：
                        <br />
                        <span className="text-blue-600 font-medium break-all">{userEmail}</span>
                      </p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <span className="text-white font-bold text-2xl">2</span>
                      </div>
                      <h5 className="text-lg font-bold text-gray-800 mb-3">立即开始使用</h5>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        这是您的专属账号，600次消息/月，无需安装插件，开箱即用
                      </p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-200 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                        <span className="text-white font-bold text-2xl">3</span>
                      </div>
                      <h5 className="text-lg font-bold text-gray-800 mb-3">账号有效期</h5>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        账号有效期为一个月，到期后可重新购买账号
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 重要提示和客服联系 */}
              <div className="grid md:grid-cols-2 gap-6 mb-8">
                {/* 重要提示 */}
                <div className="bg-gradient-to-br from-orange-50 to-yellow-50 border border-orange-200 rounded-2xl p-6 shadow-lg">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-orange-100 rounded-xl">
                      <AlertTriangle className="w-6 h-6 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-orange-800 font-bold text-lg mb-3">
                        📧 重要提示
                      </p>
                      <p className="text-orange-700 text-sm leading-relaxed">
                        如果您在邮箱中没有收到账号信息（请检查垃圾邮件文件夹），
                        请检查自己的VPN节点，关闭或开启VPN多尝试几次。
                      </p>
                    </div>
                  </div>
                </div>

                {/* 客服联系方式 */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6 shadow-lg">
                  <div className="flex items-start space-x-4">
                    <div className="p-2 bg-blue-100 rounded-xl">
                      <MessageCircle className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-blue-800 font-bold text-lg mb-2">需要帮助？</p>
                      <p className="text-blue-600 text-sm mb-4">联系客服获得专业支持</p>
                      <button className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-100 hover:bg-blue-200 border border-blue-300 rounded-xl text-blue-700 hover:text-blue-800 transition-all duration-300 transform hover:scale-105">
                        <span className="font-medium"><EMAIL></span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* 确认按钮 */}
              <div className="text-center">
                <button
                  onClick={onClose}
                  className="px-12 py-4 bg-gradient-to-r from-emerald-500 via-green-500 to-emerald-600 hover:from-emerald-600 hover:via-green-600 hover:to-emerald-700 text-white font-bold text-lg rounded-2xl transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 relative overflow-hidden group"
                >
                  <span className="relative z-10">🎉 我知道了，开始使用！</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <p className="text-gray-500 text-sm mt-4">
                  感谢您选择 AugmentCode，祝您编程愉快！
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 使用 Portal 将模态框渲染到 document.body
  return createPortal(modalContent, document.body);
}
